const { sql, query, queryWithParams } = require('../service/index.js')

class Feedbackcontroller {
  async create(ctx, next) {
    try {
      const { FeedbackType, FeedbackContent, FeedbackImg = '' } = ctx.request.body
      if (!FeedbackType || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `INSERT INTO dbo.[Feedback] 
     (FeedbackType,FeedbackContent,FeedbackUserID,LastModifyUserID,FeedbackImg) 
     values (@FeedbackType, @FeedbackContent, @FeedbackUserID, @LastModifyUserID, @FeedbackImg)`
      await queryWithParams(sentence, {
        FeedbackType: { type: sql.VarChar, value: FeedbackType },
        FeedbackContent: { type: sql.VarChar, value: FeedbackContent },
        FeedbackUserID: { type: sql.Int, value: ctx.user.id },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        FeedbackImg: { type: sql.VarChar, value: FeedbackImg }
      })
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }

  // 创建小区档案反馈记录
  async createRecord(ctx, next) {
    try {
      const { FeedbackType = '4', FeedbackContent, FeedbackImg = '', FileCode } = ctx.request.body
      if (!FileCode) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentenceQuery = `SELECT 
    FeedbackID,
    FeedbackType,
    FeedbackContent,
    FeedbackImg,
    LastModifyUserID,
    CreateTime,
    ModifyTime,
    createUserInfo,
    newestReply,
    CASE WHEN EXISTS (
        SELECT 1 
        FROM dbo.FeedbackReplyRead r
        WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
          AND r.UserID = @UserID
    ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
FROM (
    SELECT
        f.FeedbackID,
        f.FeedbackType,
        f.FeedbackContent,
        f.FeedbackImg,
        f.LastModifyUserID,
        f.CreateTime,
        f.ModifyTime,
        (
            SELECT TOP 1 
                id, Name, Username, Station, Path 
            FROM dbo.[User] 
            WHERE id = f.FeedbackUserID 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS createUserInfo,
        (
            SELECT TOP 1 * 
            FROM dbo.[FeedbackReply] 
            WHERE FeedbackID = f.FeedbackID 
            ORDER BY CreateTime DESC 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS newestReply
    FROM dbo.[Feedback] f 
WHERE FileCode = @FileCode
) AS sub  
ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentenceQuery, { UserID: { type: sql.Int, value: ctx.user.id }, FileCode: { type: sql.VarChar, value: FileCode } })
      if (!!recordsets[0][0]) {
        ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
      } else {
        if (!FileCode || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })
        const sentence = `INSERT INTO dbo.[Feedback] 
       (FeedbackType,FeedbackContent,FeedbackUserID,LastModifyUserID,FeedbackImg,hide,FileCode) 
       values (@FeedbackType, @FeedbackContent, @FeedbackUserID, @LastModifyUserID, @FeedbackImg, @hide, @FileCode)`
        await queryWithParams(sentence, {
          FeedbackType: { type: sql.VarChar, value: FeedbackType },
          FeedbackContent: { type: sql.VarChar, value: FeedbackContent },
          FeedbackUserID: { type: sql.Int, value: ctx.user.id },
          LastModifyUserID: { type: sql.Int, value: ctx.user.id },
          FeedbackImg: { type: sql.VarChar, value: FeedbackImg },
          FileCode: { type: sql.VarChar, value: FileCode },
          hide: { type: sql.Int, value: 0 }
        })
        const { recordsets } = await queryWithParams(sentenceQuery, { UserID: { type: sql.Int, value: ctx.user.id }, FileCode: { type: sql.VarChar, value: FileCode } })

        ctx.body = { code: 200, msg: '创建成功', data: recordsets[0][0] }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }
  async delete(ctx, next) {
    try {
      const { id } = ctx.params
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `DELETE FROM dbo.[Feedback] WHERE FeedbackID = @FeedbackID`
      await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { FeedbackID, FeedbackType, FeedbackContent, FeedbackImg = '' } = ctx.request.body
      if (!FeedbackID || !FeedbackType || !FeedbackContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `UPDATE dbo.[Feedback] SET 
          FeedbackType = @FeedbackType,
          FeedbackContent = @FeedbackContent,
          LastModifyUserID = @LastModifyUserID,
          FeedbackImg = @FeedbackImg
          WHERE
          FeedbackID = @FeedbackID`

      await queryWithParams(sentence, {
        FeedbackType: { type: sql.VarChar, value: FeedbackType },
        FeedbackContent: { type: sql.VarChar, value: FeedbackContent },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        FeedbackImg: { type: sql.VarChar, value: FeedbackImg },
        FeedbackID: { type: sql.Int, value: FeedbackID }
      })

      const sentence2 = `SELECT 
      FeedbackID,   
      FeedbackType,
      FeedbackContent,
      FeedbackImg,   
      LastModifyUserID,
      CreateTime,   
      ModifyTime,  
      ( SELECT id , Name,Username,Station,Path 
       FROM 
       dbo.[User] 
       WHERE 
       id = LastModifyUserID FOR JSON AUTO ) AS 'createUserInfo'  
       FROM dbo.[Feedback] 
       WHERE 
       FeedbackID = @FeedbackID`
      const { recordsets } = await queryWithParams(sentence2, { FeedbackID: { type: sql.Int, value: FeedbackID } })
      ctx.body = { code: 200, msg: '更新成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', error: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const sentence = `SELECT 
          FeedbackID,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          CASE WHEN EXISTS (
              SELECT 1 
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1 
                      id, Name, Username, Station, Path 
                  FROM dbo.[User] 
                  WHERE id = f.FeedbackUserID 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS createUserInfo,
              (
                  SELECT TOP 1 * 
                  FROM dbo.[FeedbackReply] 
                  WHERE FeedbackID = f.FeedbackID 
                  ORDER BY CreateTime DESC 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS newestReply
          FROM dbo.[Feedback] f
      WHERE hide = 1
      ) AS sub  
      ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  // 小区档案错误反馈列表
  async listRecord(ctx, next) {
    const { low = 'false' } = ctx.request.query
    try {
      const sentence = `SELECT 
          FeedbackID,
          FileCode,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          pendingCount,
          CASE WHEN EXISTS (
              SELECT 1 
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FileCode,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1 
                      id, Name, Username, Station, Path 
                  FROM dbo.[User] 
                  WHERE id = f.FeedbackUserID 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS createUserInfo,
              (
                  SELECT TOP 1 * 
                  FROM dbo.[FeedbackReply] 
                  WHERE FeedbackID = f.FeedbackID 
                  ORDER BY CreateTime DESC 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS newestReply,
              (
                  SELECT COUNT(*)
                  FROM dbo.FeedbackReply r
                  WHERE r.FeedbackID = f.FeedbackID
                    AND r.task = 1
                    AND r.perform = 0
              ) AS pendingCount
          FROM dbo.[Feedback] f
      WHERE FileCode IS NOT NULL AND FileCode <> ''
      ) AS sub  
      ORDER BY pendingCount DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id } })

      const data = low == 'true' ? recordsets[0].filter((i) => i.pendingCount > 0) : recordsets[0]
      ctx.body = { code: 200, msg: '查询成功', data }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  // 模糊搜索小区档案错误反馈
  async search(ctx, next) {
    const { name } = ctx.query
    try {
      const sentence = `SELECT 
          FeedbackID,
          FeedbackType,
          FeedbackContent,
          FeedbackImg,
          LastModifyUserID,
          FileCode,
          CreateTime,
          ModifyTime,
          createUserInfo,
          newestReply,
          pendingCount,
          CASE WHEN EXISTS (
              SELECT 1 
              FROM dbo.FeedbackReplyRead r
              WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
                AND r.UserID = @UserID
          ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
      FROM (
          SELECT
              f.FeedbackID,
              f.FeedbackType,
              f.FeedbackContent,
              f.FeedbackImg,
              f.LastModifyUserID,
              f.FileCode,
              f.CreateTime,
              f.ModifyTime,
              (
                  SELECT TOP 1 
                      id, Name, Username, Station, Path 
                  FROM dbo.[User] 
                  WHERE id = f.FeedbackUserID 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS createUserInfo,
              (
                  SELECT TOP 1 * 
                  FROM dbo.[FeedbackReply] 
                  WHERE FeedbackID = f.FeedbackID 
                  ORDER BY CreateTime DESC 
                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
              ) AS newestReply,
              (
                  SELECT COUNT(*)
                  FROM dbo.FeedbackReply r
                  WHERE r.FeedbackID = f.FeedbackID
                    AND r.task = 1
                    AND r.perform = 0
              ) AS pendingCount
          FROM dbo.[Feedback] f
      WHERE hide = 1 AND FileCode IS NOT NULL AND FileCode <> '' AND FeedbackContent LIKE '%${name}%'
      ) AS sub  
      ORDER BY pendingCount DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async hide(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `UPDATE dbo.[Feedback] SET hide = 0 WHERE FeedbackID = @FeedbackID`
      await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '隐藏成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '隐藏失败', error: error.message }
    }
  }
  async gain(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `SELECT 
    FeedbackID,
    FeedbackType,
    FeedbackContent,
    FeedbackImg,
    LastModifyUserID,
    CreateTime,
    ModifyTime,
    createUserInfo,
    newestReply,
    CASE WHEN EXISTS (
        SELECT 1 
        FROM dbo.FeedbackReplyRead r
        WHERE r.ReplyID = JSON_VALUE(sub.newestReply, '$.ReplyID')  
          AND r.UserID = @UserID
    ) THEN 1 ELSE 0 END AS IsReadByCurrentUser
FROM (
    SELECT
        f.FeedbackID,
        f.FeedbackType,
        f.FeedbackContent,
        f.FeedbackImg,
        f.LastModifyUserID,
        f.CreateTime,
        f.ModifyTime,
        (
            SELECT TOP 1 
                id, Name, Username, Station, Path 
            FROM dbo.[User] 
            WHERE id = f.FeedbackUserID 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS createUserInfo,
        (
            SELECT TOP 1 * 
            FROM dbo.[FeedbackReply] 
            WHERE FeedbackID = f.FeedbackID 
            ORDER BY CreateTime DESC 
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER 
        ) AS newestReply
    FROM dbo.[Feedback] f
WHERE FeedbackID = @FeedbackID
) AS sub  
ORDER BY FeedbackID DESC;`
      const { recordsets } = await queryWithParams(sentence, { UserID: { type: sql.Int, value: ctx.user.id }, FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      console.log(error)
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

class Replycontroller {
  async create(ctx, next) {
    try {
      const { FeedbackID, ReplyContent, ReplyImg = '', task = 0, perform = 0 } = ctx.request.body
      if (!ReplyContent) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `INSERT INTO dbo.[FeedbackReply] 
     (FeedbackID, ReplyContent, ReplyUserID, LastModifyUserID, ReplyImg, task, perform) 
     values 
     (@FeedbackID, @ReplyContent, @ReplyUserID, @LastModifyUserID, @ReplyImg, @task, @perform)`

      await queryWithParams(sentence, {
        FeedbackID: { type: sql.Int, value: FeedbackID },
        ReplyContent: { type: sql.VarChar, value: ReplyContent },
        ReplyUserID: { type: sql.Int, value: ctx.user.id },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        task: { type: sql.Int, value: task },
        perform: { type: sql.Int, value: perform },
        ReplyImg: { type: sql.VarChar, value: ReplyImg }
      })
      ctx.body = { code: 200, msg: '发送成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '发送失败', error: error.message }
    }
  }
  async delete(ctx, next) {
    try {
      const { id } = ctx.params
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `DELETE FROM dbo.[FeedbackReply] WHERE ReplyID = @ReplyID`
      await queryWithParams(sentence, { ReplyID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { ReplyID, ReplyContent, ReplyImg = '', perform, remark = '' } = ctx.request.body
      if (!ReplyID || !ReplyContent) return (ctx.body = { code: 400, msg: '参数缺失' })

      const sentence = `UPDATE dbo.[FeedbackReply] SET 
      ReplyContent = @ReplyContent,
      LastModifyUserID = @LastModifyUserID,
      perform = @perform,
      ReplyImg = @ReplyImg,
      remark = @remark
      WHERE
      ReplyID = @ReplyID`

      await queryWithParams(sentence, {
        ReplyContent: { type: sql.VarChar, value: ReplyContent },
        LastModifyUserID: { type: sql.Int, value: ctx.user.id },
        ReplyImg: { type: sql.VarChar, value: ReplyImg },
        ReplyID: { type: sql.Int, value: ReplyID },
        perform: { type: sql.Int, value: perform },
        remark: { type: sql.VarChar, value: remark }
      })

      const sentence2 = `SELECT *, 
      ( SELECT id , Name,Username,Station,Path FROM dbo.[User] 
      WHERE id = LastModifyUserID FOR JSON AUTO ) AS 'createUserInfo'  
      FROM dbo.[FeedbackReply] 
      WHERE ReplyID = @ReplyID`

      const { recordsets } = await queryWithParams(sentence2, { ReplyID: { type: sql.Int, value: ReplyID } })
      ctx.body = { code: 200, msg: '更新成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', error: error.message }
    }
  }
  async list(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `SELECT *,
    ( SELECT id , Name,Username,Station,Path FROM dbo.[User] WHERE id = ReplyUserID FOR JSON AUTO ) AS 'createUserInfo'  
FROM   
    dbo.[FeedbackReply]  
WHERE   
    hide = 1 AND FeedbackID = @FeedbackID
ORDER BY   
    ReplyID DESC;`
      const { recordsets } = await queryWithParams(sentence, { FeedbackID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }

  // 查询回复列表内需要处理的问题点数量
  async pendingCount(ctx, next) {
    try {
      const sentence = `SELECT COUNT(*) AS 'count' FROM dbo.[FeedbackReply] WHERE task = 1 AND perform = 0`
      const { recordsets } = await queryWithParams(sentence, {})
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      this.ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async hide(ctx, next) {
    const { id } = ctx.params
    try {
      const sentence = `UPDATE dbo.[FeedbackReply] SET hide = 0 WHERE ReplyID = @ReplyID`
      await queryWithParams(sentence, { ReplyID: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
}

class ReplyReadcontroller {
  async create(ctx, next) {
    try {
      const { ReplyID } = ctx.request.body
      if (!ReplyID) return (ctx.body = { code: 400, msg: '参数错误' })
      const verifyParameter = { UserID: { type: sql.Int, value: ctx.user.id }, ReplyID: { type: sql.Int, value: ReplyID } }
      const sentenceQuery = `SELECT * FROM dbo.[FeedbackReplyRead] WHERE ReplyID = @ReplyID AND UserID = @UserID`
      const { recordsets } = await queryWithParams(sentenceQuery, verifyParameter)
      if (recordsets[0].length) return (ctx.body = { code: 200, msg: '已存在' })
      const sentence = `INSERT INTO dbo.[FeedbackReplyRead] (UserID, ReplyID) VALUES (@UserID, @ReplyID)`
      await queryWithParams(sentence, verifyParameter)
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }
  async gain(ctx, next) {
    const { ReplyID } = ctx.params
    try {
      const sentence = `SELECT * FROM dbo.[FeedbackReplyRead] WHERE ReplyID = ${ReplyID} AND UserID = ${ctx.user.id}`
      const { recordsets } = await sql.query(sentence)
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async list(ctx, next) {
    try {
      const { ReplyID } = ctx.params
      const sentence = `SELECT u.id , u.Username ,u.Name,u.Station,u.path FROM dbo.FeedbackReplyRead frr INNER JOIN dbo.[User] u ON frr.UserID = u.id WHERE 
    frr.ReplyID = ${ReplyID} ORDER BY u.id DESC;`
      const { recordsets } = await sql.query(sentence)

      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

const feedback = new Feedbackcontroller()
const reply = new Replycontroller()
const read = new ReplyReadcontroller()
module.exports = { feedback, reply, read }
